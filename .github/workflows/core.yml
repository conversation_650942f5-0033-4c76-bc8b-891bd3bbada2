name: Core Tests
on:
  push:
    paths:
      - .github/**
      - devscripts/**
      - test/**
      - yt_dlp/**.py
      - '!yt_dlp/extractor/**.py'
      - yt_dlp/extractor/__init__.py
      - yt_dlp/extractor/common.py
      - yt_dlp/extractor/extractors.py
  pull_request:
    paths:
      - .github/**
      - devscripts/**
      - test/**
      - yt_dlp/**.py
      - '!yt_dlp/extractor/**.py'
      - yt_dlp/extractor/__init__.py
      - yt_dlp/extractor/common.py
      - yt_dlp/extractor/extractors.py
permissions:
  contents: read

concurrency:
  group: core-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: ${{ github.event_name == 'pull_request' }}

jobs:
  tests:
    name: Core Tests
    if: "!contains(github.event.head_commit.message, 'ci skip')"
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
        # CPython 3.9 is in quick-test
        python-version: ['3.10', '3.11', '3.12', '3.13', pypy-3.10]
        include:
        # atleast one of each CPython/PyPy tests must be in windows
        - os: windows-latest
          python-version: '3.9'
        - os: windows-latest
          python-version: '3.10'
        - os: windows-latest
          python-version: '3.12'
        - os: windows-latest
          python-version: '3.13'
        - os: windows-latest
          python-version: pypy-3.10
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install test requirements
      run: python3 ./devscripts/install_deps.py --include test --include curl-cffi
    - name: Run tests
      timeout-minutes: 15
      continue-on-error: False
      run: |
        python3 -m yt_dlp -v || true  # Print debug head
        python3 ./devscripts/run_tests.py --pytest-args '--reruns 2 --reruns-delay 3.0' core
