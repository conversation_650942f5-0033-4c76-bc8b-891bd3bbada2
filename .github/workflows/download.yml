name: Download Tests
on: [push, pull_request]
permissions:
  contents: read

jobs:
  quick:
    name: Quick Download Tests
    if: "contains(github.event.head_commit.message, 'ci run dl')"
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: 3.9
    - name: Install test requirements
      run: python3 ./devscripts/install_deps.py --include dev
    - name: Run tests
      continue-on-error: true
      run: python3 ./devscripts/run_tests.py download

  full:
    name: Full Download Tests
    if: "contains(github.event.head_commit.message, 'ci run dl all')"
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: true
      matrix:
        os: [ubuntu-latest]
        python-version: ['3.10', '3.11', '3.12', '3.13', pypy-3.10]
        include:
        # atleast one of each CPython/PyPy tests must be in windows
        - os: windows-latest
          python-version: '3.9'
        - os: windows-latest
          python-version: pypy-3.10
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install test requirements
      run: python3 ./devscripts/install_deps.py --include dev
    - name: Run tests
      continue-on-error: true
      run: python3 ./devscripts/run_tests.py download
