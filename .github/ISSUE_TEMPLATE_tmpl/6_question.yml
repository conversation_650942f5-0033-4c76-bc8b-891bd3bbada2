name: Ask question
description: Ask a question about using yt-dlp
labels: [question]
body:
  %(no_skip)s
  - type: markdown
    attributes:
      value: |
        ### Make sure you are **only** asking a question and not reporting a bug or requesting a feature.
        If your question contains "isn't working" or "can you add", this is most likely the wrong template.
        If you are in doubt whether this is the right template, **USE ANOTHER TEMPLATE**!
  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: |
        Carefully read and work through this check list in order to prevent the most common mistakes and misuse of yt-dlp:
      options:
        - label: I'm asking a question and **not** reporting a bug or requesting a feature
          required: true
        - label: I've looked through the [README](https://github.com/yt-dlp/yt-dlp#readme)
          required: true
        - label: I've verified that I have **updated yt-dlp to nightly or master** ([update instructions](https://github.com/yt-dlp/yt-dlp#update-channels))
          required: true
        - label: I've searched [known issues](https://github.com/yt-dlp/yt-dlp/issues/3766), [the FAQ](https://github.com/yt-dlp/yt-dlp/wiki/FAQ), and the [bugtracker](https://github.com/yt-dlp/yt-dlp/issues?q=is%%3Aissue%%20-label%%3Aspam%%20%%20) for similar questions **including closed ones**. DO NOT post duplicates
          required: true
  - type: textarea
    id: question
    attributes:
      label: Please make sure the question is worded well enough to be understood
      description: See [is-the-description-of-the-issue-itself-sufficient](https://github.com/yt-dlp/yt-dlp/blob/master/CONTRIBUTING.md#is-the-description-of-the-issue-itself-sufficient)
      placeholder: Provide any additional information and as much context and examples as possible
    validations:
      required: true
  %(verbose_optional)s
