name: Feature request
description: Request a new feature unrelated to any particular site or extractor
labels: [triage, enhancement]
body:
  %(no_skip)s
  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: |
        Carefully read and work through this check list in order to prevent the most common mistakes and misuse of yt-dlp:
      options:
        - label: I'm requesting a feature unrelated to a specific site
          required: true
        - label: I've looked through the [README](https://github.com/yt-dlp/yt-dlp#readme)
          required: true
        - label: I've verified that I have **updated yt-dlp to nightly or master** ([update instructions](https://github.com/yt-dlp/yt-dlp#update-channels))
          required: true
        - label: I've searched the [bugtracker](https://github.com/yt-dlp/yt-dlp/issues?q=is%%3Aissue%%20-label%%3Aspam%%20%%20) for similar requests **including closed ones**. DO NOT post duplicates
          required: true
  - type: textarea
    id: description
    attributes:
      label: Provide a description that is worded well enough to be understood
      description: See [is-the-description-of-the-issue-itself-sufficient](https://github.com/yt-dlp/yt-dlp/blob/master/CONTRIBUTING.md#is-the-description-of-the-issue-itself-sufficient)
      placeholder: Provide any additional information, any suggested solutions, and as much context and examples as possible
    validations:
      required: true
  %(verbose_optional)s
