name: Broken site support
description: Report issue with yt-dlp on a supported site
labels: [triage, site-bug]
body:
  %(no_skip)s
  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: |
        Carefully read and work through this check list in order to prevent the most common mistakes and misuse of yt-dlp:
      options:
        - label: I'm reporting that yt-dlp is broken on a **supported** site
          required: true
        - label: I've verified that I have **updated yt-dlp to nightly or master** ([update instructions](https://github.com/yt-dlp/yt-dlp#update-channels))
          required: true
        - label: I've checked that all provided URLs are playable in a browser with the same IP and same login details
          required: true
        - label: I've checked that all URLs and arguments with special characters are [properly quoted or escaped](https://github.com/yt-dlp/yt-dlp/wiki/FAQ#video-url-contains-an-ampersand--and-im-getting-some-strange-output-1-2839-or-v-is-not-recognized-as-an-internal-or-external-command)
          required: true
        - label: I've searched [known issues](https://github.com/yt-dlp/yt-dlp/issues/3766), [the FAQ](https://github.com/yt-dlp/yt-dlp/wiki/FAQ), and the [bugtracker](https://github.com/yt-dlp/yt-dlp/issues?q=is%%3Aissue%%20-label%%3Aspam%%20%%20) for similar issues **including closed ones**. DO NOT post duplicates
          required: true
        - label: I've read about [sharing account credentials](https://github.com/yt-dlp/yt-dlp/blob/master/CONTRIBUTING.md#are-you-willing-to-share-account-details-if-needed) and I'm willing to share it if required
  - type: input
    id: region
    attributes:
      label: Region
      description: Enter the country/region that the site is accessible from
      placeholder: India
  - type: textarea
    id: description
    attributes:
      label: Provide a description that is worded well enough to be understood
      description: See [is-the-description-of-the-issue-itself-sufficient](https://github.com/yt-dlp/yt-dlp/blob/master/CONTRIBUTING.md#is-the-description-of-the-issue-itself-sufficient)
      placeholder: Provide any additional information, any suggested solutions, and as much context and examples as possible
    validations:
      required: true
  %(verbose)s
