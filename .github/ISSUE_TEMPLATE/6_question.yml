name: Ask question
description: Ask a question about using yt-dlp
labels: [question]
body:
  - type: markdown
    attributes:
      value: |
        > [!IMPORTANT]
        > Not providing the required (*) information or removing the template will result in your issue being closed and ignored.
  - type: markdown
    attributes:
      value: |
        ### Make sure you are **only** asking a question and not reporting a bug or requesting a feature.
        If your question contains "isn't working" or "can you add", this is most likely the wrong template.
        If you are in doubt whether this is the right template, **USE ANOTHER TEMPLATE**!
  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: |
        Carefully read and work through this check list in order to prevent the most common mistakes and misuse of yt-dlp:
      options:
        - label: I'm asking a question and **not** reporting a bug or requesting a feature
          required: true
        - label: I've looked through the [README](https://github.com/yt-dlp/yt-dlp#readme)
          required: true
        - label: I've verified that I have **updated yt-dlp to nightly or master** ([update instructions](https://github.com/yt-dlp/yt-dlp#update-channels))
          required: true
        - label: I've searched [known issues](https://github.com/yt-dlp/yt-dlp/issues/3766), [the FAQ](https://github.com/yt-dlp/yt-dlp/wiki/FAQ), and the [bugtracker](https://github.com/yt-dlp/yt-dlp/issues?q=is%3Aissue%20-label%3Aspam%20%20) for similar questions **including closed ones**. DO NOT post duplicates
          required: true
  - type: textarea
    id: question
    attributes:
      label: Please make sure the question is worded well enough to be understood
      description: See [is-the-description-of-the-issue-itself-sufficient](https://github.com/yt-dlp/yt-dlp/blob/master/CONTRIBUTING.md#is-the-description-of-the-issue-itself-sufficient)
      placeholder: Provide any additional information and as much context and examples as possible
    validations:
      required: true
  - type: checkboxes
    id: verbose
    attributes:
      label: Provide verbose output that clearly demonstrates the problem
      description: |
        This is mandatory unless absolutely impossible to provide. If you are unable to provide the output, please explain why.
      options:
        - label: Run **your** yt-dlp command with **-vU** flag added (`yt-dlp -vU <your command line>`)
        - label: "If using API, add `'verbose': True` to `YoutubeDL` params instead"
          required: false
        - label: Copy the WHOLE output (starting with `[debug] Command-line config`) and insert it below
  - type: textarea
    id: log
    attributes:
      label: Complete Verbose Output
      description: |
        It should start like this:
      placeholder: |
        [debug] Command-line config: ['-vU', 'https://www.youtube.com/watch?v=BaW_jenozKc']
        [debug] Encodings: locale cp65001, fs utf-8, pref cp65001, out utf-8, error utf-8, screen utf-8
        [debug] yt-dlp version nightly@... from yt-dlp/yt-dlp-nightly-builds [1a176d874] (win_exe)
        [debug] Python 3.10.11 (CPython AMD64 64bit) - Windows-10-10.0.20348-SP0 (OpenSSL 1.1.1t  7 Feb 2023)
        [debug] exe versions: ffmpeg 7.0.2 (setts), ffprobe 7.0.2
        [debug] Optional libraries: Cryptodome-3.21.0, brotli-1.1.0, certifi-2024.08.30, curl_cffi-0.5.10, mutagen-1.47.0, requests-2.32.3, sqlite3-3.40.1, urllib3-2.2.3, websockets-13.1
        [debug] Proxy map: {}
        [debug] Request Handlers: urllib, requests, websockets, curl_cffi
        [debug] Loaded 1838 extractors
        [debug] Fetching release info: https://api.github.com/repos/yt-dlp/yt-dlp/releases/latest
        Latest version: nightly@... from yt-dlp/yt-dlp-nightly-builds
        yt-dlp is up to date (nightly@... from yt-dlp/yt-dlp-nightly-builds)
        [youtube] Extracting URL: https://www.youtube.com/watch?v=BaW_jenozKc
        <more lines>
      render: shell
