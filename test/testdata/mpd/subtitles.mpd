<?xml version="1.0" encoding="utf-8"?>
<!-- Created with Unified Streaming Platform (version=1.10.18-20255) -->
<MPD
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="urn:mpeg:dash:schema:mpd:2011"
  xsi:schemaLocation="urn:mpeg:dash:schema:mpd:2011 http://standards.iso.org/ittf/PubliclyAvailableStandards/MPEG-DASH_schema_files/DASH-MPD.xsd"
  type="static"
  mediaPresentationDuration="PT14M48S"
  maxSegmentDuration="PT1M"
  minBufferTime="PT10S"
  profiles="urn:mpeg:dash:profile:isoff-live:2011">
  <Period
    id="1"
    duration="PT14M48S">
    <BaseURL>dash/</BaseURL>
    <AdaptationSet
      id="1"
      group="1"
      contentType="audio"
      segmentAlignment="true"
      audioSamplingRate="48000"
      mimeType="audio/mp4"
      codecs="mp4a.40.2"
      startWithSAP="1">
      <AudioChannelConfiguration
        schemeIdUri="urn:mpeg:dash:23003:3:audio_channel_configuration:2011"
        value="2" />
      <Role schemeIdUri="urn:mpeg:dash:role:2011" value="main" />
      <SegmentTemplate
        timescale="48000"
        initialization="3144-kZT4LWMQw6Rh7Kpd-$RepresentationID$.dash"
        media="3144-kZT4LWMQw6Rh7Kpd-$RepresentationID$-$Time$.dash">
        <SegmentTimeline>
          <S t="0" d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="96256" r="2" />
          <S d="95232" />
          <S d="3584" />
        </SegmentTimeline>
      </SegmentTemplate>
      <Representation
        id="audio=128001"
        bandwidth="128001">
      </Representation>
    </AdaptationSet>
    <AdaptationSet
      id="2"
      group="3"
      contentType="text"
      lang="en"
      mimeType="application/mp4"
      codecs="stpp"
      startWithSAP="1">
      <Role schemeIdUri="urn:mpeg:dash:role:2011" value="subtitle" />
      <SegmentTemplate
        timescale="1000"
        initialization="3144-kZT4LWMQw6Rh7Kpd-$RepresentationID$.dash"
        media="3144-kZT4LWMQw6Rh7Kpd-$RepresentationID$-$Time$.dash">
        <SegmentTimeline>
          <S t="0" d="60000" r="9" />
          <S d="24000" />
        </SegmentTimeline>
      </SegmentTemplate>
      <Representation
        id="textstream_eng=1000"
        bandwidth="1000">
      </Representation>
    </AdaptationSet>
    <AdaptationSet
      id="3"
      group="2"
      contentType="video"
      par="960:409"
      minBandwidth="100000"
      maxBandwidth="4482000"
      maxWidth="1689"
      maxHeight="720"
      segmentAlignment="true"
      mimeType="video/mp4"
      codecs="avc1.4D401F"
      startWithSAP="1">
      <Role schemeIdUri="urn:mpeg:dash:role:2011" value="main" />
      <SegmentTemplate
        timescale="12288"
        initialization="3144-kZT4LWMQw6Rh7Kpd-$RepresentationID$.dash"
        media="3144-kZT4LWMQw6Rh7Kpd-$RepresentationID$-$Time$.dash">
        <SegmentTimeline>
          <S t="0" d="24576" r="443" />
        </SegmentTimeline>
      </SegmentTemplate>
      <Representation
        id="video=100000"
        bandwidth="100000"
        width="336"
        height="144"
        sar="2880:2863"
        scanType="progressive">
      </Representation>
      <Representation
        id="video=326000"
        bandwidth="326000"
        width="562"
        height="240"
        sar="115200:114929"
        scanType="progressive">
      </Representation>
      <Representation
        id="video=698000"
        bandwidth="698000"
        width="844"
        height="360"
        sar="86400:86299"
        scanType="progressive">
      </Representation>
      <Representation
        id="video=1493000"
        bandwidth="1493000"
        width="1126"
        height="480"
        sar="230400:230267"
        scanType="progressive">
      </Representation>
      <Representation
        id="video=4482000"
        bandwidth="4482000"
        width="1688"
        height="720"
        sar="86400:86299"
        scanType="progressive">
      </Representation>
    </AdaptationSet>
  </Period>
</MPD>
