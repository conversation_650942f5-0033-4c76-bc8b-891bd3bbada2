import re
import urllib.parse

from .common import InfoExtractor
from ..utils import (
    ExtractorError,
    determine_ext,
    dict_get,
    js_to_json,
    strip_jsonp,
    try_get,
    unified_strdate,
    update_url_query,
    url_or_none,
    urlhandle_detect_ext,
)


class WDRIE(InfoExtractor):
    __API_URL_TPL = '//deviceids-medp.wdr.de/ondemand/%s/%s'
    _VALID_URL = r'''(?x)https?://
        (?:deviceids-medp\.wdr\.de/ondemand/\d+/|
           kinder\.wdr\.de/(?!mediathek/)[^#?]+-)
        (?P<id>\d+)\.(?:js|assetjsonp)
    '''
    _GEO_COUNTRIES = ['DE']
    _TESTS = [{
        'url': 'http://deviceids-medp.wdr.de/ondemand/155/1557833.js',
        'info_dict': {
            'id': 'mdb-1557833',
            'ext': 'mp4',
            'title': 'Biathlon-Staffel verpasst Podest bei Olympia-Generalprobe',
            'upload_date': '20180112',
        },
    }]

    def _asset_url(self, wdr_id):
        id_len = max(len(wdr_id), 5)
        return ''.join(('https:', self.__API_URL_TPL % (wdr_id[:id_len - 4], wdr_id), '.js'))

    def _real_extract(self, url):
        video_id = self._match_id(url)

        if url.startswith('wdr:'):
            video_id = url[4:]
            url = self._asset_url(video_id)

        metadata = self._download_json(
            url, video_id, transform_source=strip_jsonp)

        is_live = metadata.get('mediaType') == 'live'

        tracker_data = metadata['trackerData']
        title = tracker_data['trackerClipTitle']
        media_resource = metadata['mediaResource']

        formats = []
        subtitles = {}

        # check if the metadata contains a direct URL to a file
        for kind, media in media_resource.items():
            if kind == 'captionsHash':
                for ext, url in media.items():
                    subtitles.setdefault('de', []).append({
                        'url': url,
                        'ext': ext,
                    })
                continue

            if kind not in ('dflt', 'alt'):
                continue
            if not isinstance(media, dict):
                continue

            for tag_name, medium_url in media.items():
                if tag_name not in ('videoURL', 'audioURL'):
                    continue

                ext = determine_ext(medium_url)
                if ext == 'm3u8':
                    formats.extend(self._extract_m3u8_formats(
                        medium_url, video_id, 'mp4', 'm3u8_native',
                        m3u8_id='hls'))
                elif ext == 'f4m':
                    manifest_url = update_url_query(
                        medium_url, {'hdcore': '3.2.0', 'plugin': 'aasp-3.2.0.77.18'})
                    formats.extend(self._extract_f4m_formats(
                        manifest_url, video_id, f4m_id='hds', fatal=False))
                elif ext == 'smil':
                    formats.extend(self._extract_smil_formats(
                        medium_url, 'stream', fatal=False))
                else:
                    a_format = {
                        'url': medium_url,
                    }
                    if ext == 'unknown_video':
                        urlh = self._request_webpage(
                            medium_url, video_id, note='Determining extension')
                        ext = urlhandle_detect_ext(urlh)
                        a_format['ext'] = ext
                    formats.append(a_format)

        caption_url = media_resource.get('captionURL')
        if caption_url:
            subtitles['de'] = [{
                'url': caption_url,
                'ext': 'ttml',
            }]
        captions_hash = media_resource.get('captionsHash')
        if isinstance(captions_hash, dict):
            for ext, format_url in captions_hash.items():
                format_url = url_or_none(format_url)
                if not format_url:
                    continue
                subtitles.setdefault('de', []).append({
                    'url': format_url,
                    'ext': determine_ext(format_url, None) or ext,
                })

        return {
            'id': tracker_data.get('trackerClipId', video_id),
            'title': title,
            'alt_title': tracker_data.get('trackerClipSubcategory'),
            'formats': formats,
            'subtitles': subtitles,
            'upload_date': unified_strdate(tracker_data.get('trackerClipAirTime')),
            'is_live': is_live,
        }


class WDRPageIE(WDRIE):  # XXX: Do not subclass from concrete IE
    _MAUS_REGEX = r'https?://(?:www\.)wdrmaus.de/(?:[^/]+/)*?(?P<maus_id>[^/?#.]+)(?:/?|/index\.php5|\.php5)$'
    _PAGE_REGEX = r'/(?:mediathek/)?(?:[^/]+/)*(?P<display_id>[^/]+)\.html'
    _VALID_URL = r'https?://(?:www\d?\.)?(?:(?:kinder\.)?wdr\d?|sportschau)\.de' + _PAGE_REGEX + '|' + _MAUS_REGEX

    _TESTS = [
        {
            'url': 'http://www1.wdr.de/mediathek/video/sendungen/doku-am-freitag/video-geheimnis-aachener-dom-100.html',
            # HDS download, MD5 is unstable
            'info_dict': {
                'id': 'mdb-1058683',
                'ext': 'flv',
                'display_id': 'doku-am-freitag/video-geheimnis-aachener-dom-100',
                'title': 'Geheimnis Aachener Dom',
                'alt_title': 'Doku am Freitag',
                'upload_date': '20160304',
                'description': 'md5:87be8ff14d8dfd7a7ee46f0299b52318',
                'is_live': False,
                'subtitles': {'de': [{
                    'url': 'http://ondemand-ww.wdr.de/medp/fsk0/105/1058683/1058683_12220974.xml',
                    'ext': 'ttml',
                }]},
            },
            'skip': 'HTTP Error 404: Not Found',
        },
        {
            'url': 'http://www1.wdr.de/mediathek/audio/wdr3/wdr3-gespraech-am-samstag/audio-schriftstellerin-juli-zeh-100.html',
            'md5': 'f4c1f96d01cf285240f53ea4309663d8',
            'info_dict': {
                'id': 'mdb-1072000',
                'ext': 'mp3',
                'display_id': 'wdr3-gespraech-am-samstag/audio-schriftstellerin-juli-zeh-100',
                'title': 'Schriftstellerin Juli Zeh',
                'alt_title': 'WDR 3 Gespräch am Samstag',
                'upload_date': '20160312',
                'description': 'md5:e127d320bc2b1f149be697ce044a3dd7',
                'is_live': False,
                'subtitles': {},
            },
            'skip': 'HTTP Error 404: Not Found',
        },
        {
            # FIXME: Asset JSON is directly embedded in webpage
            'url': 'http://www1.wdr.de/mediathek/video/live/index.html',
            'info_dict': {
                'id': 'mdb-2296252',
                'ext': 'mp4',
                'title': r're:^WDR Fernsehen im Livestream (?:\(nur in Deutschland erreichbar\) )?[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}$',
                'alt_title': 'WDR Fernsehen Live',
                'upload_date': '20201112',
                'is_live': True,
            },
            'params': {
                'skip_download': True,  # m3u8 download
            },
        },
        {
            'url': 'http://www1.wdr.de/mediathek/video/sendungen/aktuelle-stunde/aktuelle-stunde-120.html',
            'playlist_mincount': 6,
            'info_dict': {
                'id': 'aktuelle-stunde-120',
            },
        },
        {
            'url': 'http://www.wdrmaus.de/aktuelle-sendung/index.php5',
            'info_dict': {
                'id': 'mdb-2627637',
                'ext': 'mp4',
                'upload_date': 're:^[0-9]{8}$',
                'title': 're:^Die Sendung (?:mit der Maus )?vom [0-9.]{10}$',
            },
            'skip': 'The id changes from week to week because of the new episode',
        },
        {
            'url': 'http://www.wdrmaus.de/filme/sachgeschichten/achterbahn.php5',
            'md5': '803138901f6368ee497b4d195bb164f2',
            'info_dict': {
                'id': 'mdb-186083',
                'ext': 'mp4',
                'upload_date': '20130919',
                'title': 'Sachgeschichte - Achterbahn ',
            },
            'skip': 'HTTP Error 404: Not Found',
        },
        {
            'url': 'http://www1.wdr.de/radio/player/radioplayer116~_layout-popupVersion.html',
            # Live stream, MD5 unstable
            'info_dict': {
                'id': 'mdb-869971',
                'ext': 'mp4',
                'title': r're:^COSMO Livestream [0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}$',
                'alt_title': 'COSMO Livestream',
                'live_status': 'is_live',
                'upload_date': '20160101',
            },
            'params': {
                'skip_download': True,  # m3u8 download
            },
        },
        {
            'url': 'http://www.sportschau.de/handballem2018/handball-nationalmannschaft-em-stolperstein-vorrunde-100.html',
            'info_dict': {
                'id': 'mdb-1556012',
                'ext': 'mp4',
                'title': 'DHB-Vizepräsident Bob Hanning - "Die Weltspitze ist extrem breit"',
                'upload_date': '20180111',
            },
            'params': {
                'skip_download': True,
            },
            'skip': 'HTTP Error 404: Not Found',
        },
        {
            'url': 'http://www.sportschau.de/handballem2018/audio-vorschau---die-handball-em-startet-mit-grossem-favoritenfeld-100.html',
            'only_matching': True,
        },
        {
            'url': 'https://kinder.wdr.de/tv/die-sendung-mit-dem-elefanten/av/video-folge---astronaut-100.html',
            'only_matching': True,
        },
        {
            'url': 'https://www1.wdr.de/mediathek/video/sendungen/rockpalast/video-baroness---freak-valley-festival--100.html',
            'info_dict': {
                'id': 'mdb-2741028',
                'ext': 'mp4',
                'title': 'Baroness - Freak Valley Festival 2022',
                'alt_title': 'Rockpalast',
                'upload_date': '20220725',
            },
        },
    ]

    def _real_extract(self, url):
        mobj = self._match_valid_url(url)
        display_id = dict_get(mobj.groupdict(), ('display_id', 'maus_id'), 'wdrmaus')
        webpage = self._download_webpage(url, display_id)

        entries = []

        # Article with several videos

        # for wdr.de the data-extension-ard is in a tag with the class "mediaLink"
        # for wdr.de radio players, in a tag with the class "wdrrPlayerPlayBtn"
        # for wdrmaus, in a tag with the class "videoButton" (previously a link
        # to the page in a multiline "videoLink"-tag)
        for mobj in re.finditer(
            r'''(?sx)class=
                    (?:
                        (["\'])(?:mediaLink|wdrrPlayerPlayBtn|videoButton)\b.*?\1[^>]+|
                        (["\'])videoLink\b.*?\2[\s]*>\n[^\n]*
                    )data-extension(?:-ard)?=(["\'])(?P<data>(?:(?!\3).)+)\3
                    ''', webpage):
            media_link_obj = self._parse_json(
                mobj.group('data'), display_id, transform_source=js_to_json,
                fatal=False)
            if not media_link_obj:
                continue
            jsonp_url = try_get(
                media_link_obj, lambda x: x['mediaObj']['url'], str)
            if jsonp_url:
                # metadata, or player JS with ['ref'] giving WDR id, or just media, perhaps
                clip_id = media_link_obj['mediaObj'].get('ref')
                if jsonp_url.endswith('.assetjsonp'):
                    asset = self._download_json(
                        jsonp_url, display_id, fatal=False, transform_source=strip_jsonp)
                    clip_id = try_get(asset, lambda x: x['trackerData']['trackerClipId'], str)
                if clip_id:
                    jsonp_url = self._asset_url(clip_id[4:])
                entries.append(self.url_result(jsonp_url, ie=WDRIE.ie_key()))

        # Playlist (e.g. https://www1.wdr.de/mediathek/video/sendungen/aktuelle-stunde/aktuelle-stunde-120.html)
        if not entries:
            entries = [
                self.url_result(
                    urllib.parse.urljoin(url, mobj.group('href')),
                    ie=WDRPageIE.ie_key())
                for mobj in re.finditer(
                    r'<a[^>]+\bhref=(["\'])(?P<href>(?:(?!\1).)+)\1[^>]+\bdata-extension(?:-ard)?=',
                    webpage) if re.match(self._PAGE_REGEX, mobj.group('href'))
            ]

        return self.playlist_result(entries, playlist_id=display_id)


class WDRElefantIE(InfoExtractor):
    _VALID_URL = r'https?://(?:www\.)wdrmaus\.de/elefantenseite/#(?P<id>.+)'
    _TEST = {
        'url': 'http://www.wdrmaus.de/elefantenseite/#elefantenkino_wippe',
        # adaptive stream: unstable file MD5
        'info_dict': {
            'title': 'Wippe',
            'id': 'mdb-1198320',
            'ext': 'mp4',
            'upload_date': '20071003',
        },
    }

    def _real_extract(self, url):
        display_id = self._match_id(url)

        # Table of Contents seems to always be at this address, so fetch it directly.
        # The website fetches configurationJS.php5, which links to tableOfContentsJS.php5.
        table_of_contents = self._download_json(
            'https://www.wdrmaus.de/elefantenseite/data/tableOfContentsJS.php5',
            display_id)
        if display_id not in table_of_contents:
            raise ExtractorError(
                'No entry in site\'s table of contents for this URL. '
                'Is the fragment part of the URL (after the #) correct?',
                expected=True)
        xml_metadata_path = table_of_contents[display_id]['xmlPath']
        xml_metadata = self._download_xml(
            'https://www.wdrmaus.de/elefantenseite/' + xml_metadata_path,
            display_id)
        zmdb_url_element = xml_metadata.find('./movie/zmdb_url')
        if zmdb_url_element is None:
            raise ExtractorError(
                f'{display_id} is not a video', expected=True)
        return self.url_result(zmdb_url_element.text, ie=WDRIE.ie_key())


class WDRMobileIE(InfoExtractor):
    _VALID_URL = r'''(?x)
        https?://mobile-ondemand\.wdr\.de/
        .*?/fsk(?P<age_limit>[0-9]+)
        /[0-9]+/[0-9]+/
        (?P<id>[0-9]+)_(?P<title>[0-9]+)'''
    IE_NAME = 'wdr:mobile'
    _WORKING = False  # no such domain
    _TEST = {
        'url': 'http://mobile-ondemand.wdr.de/CMS2010/mdb/ondemand/weltweit/fsk0/42/421735/421735_4283021.mp4',
        'info_dict': {
            'title': '4283021',
            'id': '421735',
            'ext': 'mp4',
            'age_limit': 0,
        },
        'skip': 'Problems with loading data.',
    }

    def _real_extract(self, url):
        mobj = self._match_valid_url(url)
        return {
            'id': mobj.group('id'),
            'title': mobj.group('title'),
            'age_limit': int(mobj.group('age_limit')),
            'url': url,
            'http_headers': {
                'User-Agent': 'mobile',
            },
        }
